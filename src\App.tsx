import { DndContext } from "@dnd-kit/core";
import DragSample, { Draggable, DropBoard } from "./drag";
import SortableSample from "./sort";

function App() {
  return (
    <>
      <DragSample />

      <DndContext>
        <div style={{ display: "flex", gap: 20 }}>
          <Draggable id="draggable">
            <div>Drag Item2</div>
          </Draggable>
          <DropBoard>
            <SortableSample />
          </DropBoard>
        </div>
      </DndContext>
    </>
  );
}

export default App;
