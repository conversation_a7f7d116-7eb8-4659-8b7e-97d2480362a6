import {
  useDraggable,
  DndContext,
  useDroppable,
  Drag<PERSON>verlay,
  type DragEndEvent,
} from "@dnd-kit/core";
import { restrictToWindowEdges } from "@dnd-kit/modifiers";
import { useState } from "react";
import { createPortal } from "react-dom";

export const DropBoard = (props: { children: React.ReactNode }) => {
  const { isOver, setNodeRef } = useDroppable({
    id: "droppable",
  });
  const style = {
    color: isOver ? "green" : undefined,
    width: 300,
    border: "1px solid black",
  };

  return (
    <div ref={setNodeRef} style={style}>
      {props.children}
    </div>
  );
};

export function Draggable(props: { id: string; children: React.ReactNode }) {
  const { attributes, listeners, setNodeRef } = useDraggable({
    id: props.id,
  });

  return (
    <div ref={setNodeRef} {...listeners} {...attributes}>
      {props.children}
    </div>
  );
}

const Item = () => {
  return (
    <div style={{ width: 100, height: 100, border: "1px solid black" }}>
      Drag Item
    </div>
  );
};

const DragSample = () => {
  const [isDropped, setIsDropped] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  function handleDragEnd(event: DragEndEvent) {
    if (event.over && event.over.id === "droppable") {
      setIsDropped(true);
    }
    setIsDragging(false);
  }

  function handleDragStart() {
    setIsDragging(true);
  }

  return (
    <DndContext onDragEnd={handleDragEnd} onDragStart={handleDragStart}>
      <Draggable id="draggable">
        <Item />
      </Draggable>

      {createPortal(
        <DragOverlay
          style={{ color: "red", opacity: 0.5 }}
          modifiers={[restrictToWindowEdges]}
          dropAnimation={null}
        >
          {isDragging ? <Item /> : null}
        </DragOverlay>,
        document.body
      )}
      <DropBoard>{isDropped ? <Item /> : "Drop here"}</DropBoard>
    </DndContext>
  );
};

export default DragSample;
