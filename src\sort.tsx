import {
  DndContext,
  closestCenter,
  closestCorners,
  DragOverlay,
  type DragStartEvent,
  type DragOverEvent,
  type DragEndEvent,
  type CollisionDetection,
  type UniqueIdentifier,
  pointerWithin,
  rectIntersection,
  useDroppable,
} from "@dnd-kit/core";
import { SortableContext, useSortable, arrayMove } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { useEffect, useState, type CSSProperties } from "react";
import { createPortal } from "react-dom";

const SortableItem = ({
  id,
  styles = { padding: 10, width: 200, border: "1px solid black" },
  children,
}: {
  id: string;
  styles?: React.CSSProperties;
  children?: React.ReactNode;
}) => {
  const {
    attributes,
    listeners,
    transform,
    transition,
    isDragging,
    isOver,
    setNodeRef,
  } = useSortable({ id });

  return (
    <div
      ref={setNodeRef}
      {...attributes}
      {...listeners}
      style={
        {
          transition,
          // transform: isDragging
          //   ? `translate3d(${transform?.x}px, ${transform?.y}px, 0) scaleX(1) scaleY(1)`
          //   : "",
          opacity: isDragging ? 0.3 : 1,
          ...styles,
          borderRight: isOver ? "2px solid red" : "1px solid black",
          // 确保拖拽时保持原有尺寸
          // minHeight: styles.height || "auto",
          // minWidth: styles.width || "auto",
        } as CSSProperties
      }
    >
      {children || `item ${id}`}
    </div>
  );
};

const SortContainer = ({ id, items }: { id: string; items: string[] }) => {
  return (
    <SortableItem
      id={id}
      styles={{
        width: 200,
        padding: 10,
        border: "1px solid black",
        backgroundColor: "white",
      }}
    >
      <SortableContext items={items}>
        {items.map((item) => (
          <SortableItem
            key={item}
            id={item}
            styles={{
              width: 100,
              height: 50,
              border: "1px solid black",
              margin: "2px 0",
              backgroundColor: "white",
            }}
          />
        ))}
      </SortableContext>
    </SortableItem>
  );
};

// 创建一个静态的拖拽预览组件，避免拖拽时的样式变形
const DragPreview = ({
  activeId,
  items,
}: {
  activeId: UniqueIdentifier;
  items: (string | string[])[];
}) => {
  // 如果拖拽的是container，渲染完整的container结构
  if (typeof activeId === "string" && activeId.startsWith("container-")) {
    const containerIndex = parseInt(activeId.split("-")[1]);
    const containerItems = items[containerIndex] as string[];
    return (
      <div
        style={{
          width: 200,
          padding: 10,
          border: "1px solid black",
          background: "white",
          boxShadow: "0 4px 8px rgba(0,0,0,0.2)",
        }}
      >
        {containerItems.map((item) => (
          <div
            key={item}
            style={{
              width: 100,
              height: 50,
              border: "1px solid black",
              padding: 10,
              margin: "2px 0",
              backgroundColor: "white",
            }}
          >
            item {item}
          </div>
        ))}
      </div>
    );
  } else {
    // 普通项目的拖拽预览
    return (
      <div
        style={{
          padding: 10,
          width: 200,
          border: "1px solid black",
          background: "white",
          boxShadow: "0 4px 8px rgba(0,0,0,0.2)",
        }}
      >
        item {activeId}
      </div>
    );
  }
};

// 自定义碰撞检测算法，解决嵌套排序问题
const customCollisionDetection: CollisionDetection = (args) => {
  const { active, droppableContainers } = args;

  // 如果拖拽的是container，我们需要特殊处理
  if (typeof active.id === "string" && active.id.startsWith("container-")) {
    // 过滤掉所有container，只检测外层的排序项
    const filteredContainers = droppableContainers.filter(
      (container) => container.id !== active.id
    );

    // 使用closestCenter算法检测与外层项目的碰撞
    return closestCenter({
      ...args,
      droppableContainers: filteredContainers,
    });
  }

  const pointerCollisions = pointerWithin(args);

  // Collision detection algorithms return an array of collisions
  if (pointerCollisions.length > 0) {
    return pointerCollisions;
  }

  // 对于其他项目，使用默认的closestCorners算法
  return closestCorners(args);
};

const moveItemToRoot = (
  items: (string | string[])[],
  active: string,
  newIndex: number
) => {
  const containerIndex = items.findIndex((item) => item.includes(active));
  const container = items[containerIndex] as string[];
  const newContainer = container.filter((item) => item !== active);
  items[containerIndex] = newContainer;
  items.splice(newIndex + 1, 0, active);
  return items;
};

const moveItemToContainer = (
  items: (string | string[])[],
  over: string,
  oldIndex: number
) => {
  console.log(over, oldIndex, "move");
  // 防止将container移动到container之中
  if (items[oldIndex] instanceof Array) return items;
  const containerIndex = items.findIndex((item) => item.includes(over));
  const container = items[containerIndex] as string[];
  const newIndex = container.findIndex((item) => item === over);
  container.splice(newIndex + 1, 0, items[oldIndex] as string);
  items[containerIndex] = [...container];
  items.splice(oldIndex, 1);
  return items;
};

const moveContainerItemToOtherContainer = (
  items: (string | string[])[],
  active: string,
  over: string
) => {
  const oldContainerIndex = items.findIndex((item) => item.includes(active));
  const newContainerIndex = items.findIndex((item) => item.includes(over));
  if (oldContainerIndex === newContainerIndex) {
    const container = items[oldContainerIndex] as string[];
    const newIndex = container.findIndex((item) => item === over);
    const oldIndex = container.findIndex((item) => item === active);
    items[oldContainerIndex] = arrayMove(container, oldIndex, newIndex);
    return items;
  }
  const oldContainer = items[oldContainerIndex] as string[];
  const newContainer = items[newContainerIndex] as string[];
  const newIndex = newContainer.findIndex((item) => item === over);
  oldContainer.splice(oldContainer.indexOf(active), 1);
  newContainer.splice(newIndex + 1, 0, active);
  items[oldContainerIndex] = oldContainer;
  items[newContainerIndex] = newContainer;
  return items;
};

const SortableSample = () => {
  const [items, setItems] = useState<(string | string[])[]>([
    "1",
    "2",
    "3",
    "4",
    ["5", "6"],
    ["7", "8"],
  ]);
  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);

  // const sensors = useSensors(useSensor(PointerSensor));

  const handleDragStart = (event: DragStartEvent) => {
    console.log(event.active.id, "start");
    setActiveId(event.active.id);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    setActiveId(null);
    const { active, over } = event;
    console.log(active.id, over?.id, event, "end");
    if (!active || !over) return; // 处理边界情况

    if (
      (over.id as string).startsWith("container-") &&
      !(active.id as string).includes("container")
    ) {
      const newIndex = items.findIndex((item, index) => {
        return `container-${index}` === over.id;
      });
      const oldIndex = items.indexOf(active.id as string);
      if (newIndex === -1) return;
      if (items[newIndex].length === 0) {
        const newItems = [...items];
        newItems[newIndex] = [active.id as string];
        newItems.splice(oldIndex, 1);
        setItems(newItems);
        return;
      }
    }
    if (active.id !== over?.id) {
      // 找到拖拽项和目标项的索引
      const oldIndex = items.findIndex((item, index) => {
        if (item instanceof Array) {
          return `container-${index}` === active.id;
        }
        return item === active.id;
      });

      const newIndex = items.findIndex((item, index) => {
        if (item instanceof Array) {
          return `container-${index}` === over.id;
        }
        return item === over.id;
      });
      console.log(oldIndex, newIndex, "index");

      // 有一方的index 为-1，说明是子项
      // 如果oldIndex 为-1，说明是拖拽子项到外部
      if (oldIndex === -1 && newIndex !== -1) {
        setItems(moveItemToRoot(items, active.id as string, newIndex));
      }
      // 如果newIndex 为-1，说明是拖拽子项到容器内部
      else if (oldIndex !== -1 && newIndex === -1) {
        setItems(moveItemToContainer(items, over.id as string, oldIndex));
      }
      // 如果都为-1，则说明是容器内部互相移动
      else if (oldIndex === -1 && newIndex === -1) {
        moveContainerItemToOtherContainer(
          items,
          active.id as string,
          over.id as string
        );
      } else {
        setItems(arrayMove(items, oldIndex, newIndex));
      }
    }
  };

  return (
    <DndContext
      collisionDetection={customCollisionDetection}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <SortableContext
        items={items.map((i, index) =>
          i instanceof Array ? `container-${index}` : i.toString()
        )}
      >
        {items.map((item, index) => {
          if (item instanceof Array) {
            return (
              <SortContainer
                key={`container-${index}`}
                id={`container-${index}`}
                items={item}
              />
            );
          } else {
            return <SortableItem key={item} id={item} />;
          }
        })}
      </SortableContext>
      {/* <SortableContext items={items}>
        {items.map((item) => (
          <SortableItem key={item} id={item} />
        ))}
      </SortableContext> */}
      {/* {createPortal(
        <DragOverlay dropAnimation={null}>
          {activeId ? <DragPreview activeId={activeId} items={items} /> : null}
        </DragOverlay>,
        document.body
      )} */}
    </DndContext>
  );
};

export default SortableSample;
